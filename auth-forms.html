<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Auth Forms</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="auth-container">
        <div class="form-toggle">
            <button class="toggle-btn active" onclick="showLogin()">Login</button>
            <button class="toggle-btn" onclick="showSignup()">Sign Up</button>
        </div>

        <div class="form-container">
            <!-- Login Form -->
            <form class="auth-form active" id="loginForm">
                <div class="form-header">
                    <h2 class="form-title">Welcome Back</h2>
                    <p class="form-subtitle">Please sign in to your account</p>
                </div>

                <div class="form-group">
                    <label class="form-label" for="loginEmail">Email Address</label>
                    <input type="email" id="loginEmail" class="form-input" placeholder="Enter your email" required>
                </div>

                <div class="form-group">
                    <label class="form-label" for="loginPassword">Password</label>
                    <input type="password" id="loginPassword" class="form-input" placeholder="Enter your password" required>
                </div>

                <div class="checkbox-group">
                    <input type="checkbox" id="rememberMe" class="checkbox">
                    <label for="rememberMe" class="checkbox-label">Remember me for 30 days</label>
                </div>

                <button type="submit" class="submit-btn">Sign In</button>

                <div class="divider">
                    <span>or continue with</span>
                </div>

                <div class="social-login">
                    <button type="button" class="social-btn">
                        <span>🔍</span> Google
                    </button>
                    <button type="button" class="social-btn">
                        <span>📘</span> Facebook
                    </button>
                </div>

                <div class="forgot-password">
                    <a href="#" onclick="alert('Forgot password functionality would be implemented here')">Forgot your password?</a>
                </div>
            </form>

            <!-- Signup Form -->
            <form class="auth-form" id="signupForm">
                <div class="form-header">
                    <h2 class="form-title">Create Account</h2>
                    <p class="form-subtitle">Join us today and get started</p>
                </div>

                <div class="form-group">
                    <label class="form-label" for="signupName">Full Name</label>
                    <input type="text" id="signupName" class="form-input" placeholder="Enter your full name" required>
                </div>

                <div class="form-group">
                    <label class="form-label" for="signupEmail">Email Address</label>
                    <input type="email" id="signupEmail" class="form-input" placeholder="Enter your email" required>
                </div>

                <div class="form-group">
                    <label class="form-label" for="signupPassword">Password</label>
                    <input type="password" id="signupPassword" class="form-input" placeholder="Create a strong password" required>
                </div>

                <div class="form-group">
                    <label class="form-label" for="confirmPassword">Confirm Password</label>
                    <input type="password" id="confirmPassword" class="form-input" placeholder="Confirm your password" required>
                </div>

                <div class="checkbox-group">
                    <input type="checkbox" id="agreeTerms" class="checkbox" required>
                    <label for="agreeTerms" class="checkbox-label">I agree to the Terms of Service and Privacy Policy</label>
                </div>

                <button type="submit" class="submit-btn">Create Account</button>

                <div class="divider">
                    <span>or continue with</span>
                </div>

                <div class="social-login">
                    <button type="button" class="social-btn">
                        <span>🔍</span> Google
                    </button>
                    <button type="button" class="social-btn">
                        <span>📘</span> Facebook
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function showLogin() {
            document.getElementById('loginForm').classList.add('active');
            document.getElementById('signupForm').classList.remove('active');
            document.querySelectorAll('.toggle-btn')[0].classList.add('active');
            document.querySelectorAll('.toggle-btn')[1].classList.remove('active');
        }

        function showSignup() {
            document.getElementById('signupForm').classList.add('active');
            document.getElementById('loginForm').classList.remove('active');
            document.querySelectorAll('.toggle-btn')[1].classList.add('active');
            document.querySelectorAll('.toggle-btn')[0].classList.remove('active');
        }

        // Form submission handlers
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('Login form submitted! (This would normally send data to a server)');
        });

        document.getElementById('signupForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const password = document.getElementById('signupPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            
            if (password !== confirmPassword) {
                alert('Passwords do not match!');
                return;
            }
            
            alert('Signup form submitted! (This would normally send data to a server)');
        });
    </script>
</body>
</html>
