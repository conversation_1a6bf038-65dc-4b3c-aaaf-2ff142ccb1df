* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #000000 0%, #1a1a1a 50%, #2d2d2d 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    position: relative;
    overflow-x: hidden;
}

body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 215, 0, 0.08) 0%, transparent 50%);
    pointer-events: none;
}

.auth-container {
    background: rgba(0, 0, 0, 0.85);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 215, 0, 0.2);
    border-radius: 24px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5),
                0 0 0 1px rgba(255, 215, 0, 0.1),
                inset 0 1px 0 rgba(255, 215, 0, 0.1);
    width: 100%;
    max-width: 800px;
    min-height: 500px;
    padding: 50px 60px;
    position: relative;
    overflow: hidden;
}

.auth-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #FFD700 0%, #FFA500 50%, #FFD700 100%);
}

.auth-container::after {
    content: '';
    position: absolute;
    top: 20px;
    right: 20px;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(255, 215, 0, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    pointer-events: none;
}

.form-toggle {
    display: flex;
    margin-bottom: 40px;
    background: rgba(255, 215, 0, 0.1);
    border: 1px solid rgba(255, 215, 0, 0.2);
    border-radius: 16px;
    padding: 6px;
    backdrop-filter: blur(10px);
}

.toggle-btn {
    flex: 1;
    padding: 16px 24px;
    text-align: center;
    border: none;
    background: transparent;
    border-radius: 12px;
    cursor: pointer;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.7);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 16px;
    letter-spacing: 0.5px;
}

.toggle-btn.active {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    color: #000000;
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.3),
                0 0 0 1px rgba(255, 215, 0, 0.2);
    transform: translateY(-1px);
}

.form-container {
    position: relative;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: start;
}

.auth-form {
    display: none;
    grid-column: 1 / -1;
}

.auth-form.active {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    animation: fadeIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.form-header {
    grid-column: 1 / -1;
    text-align: center;
    margin-bottom: 20px;
}

.form-title {
    font-size: 36px;
    font-weight: 800;
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 12px;
    letter-spacing: -0.5px;
}

.form-subtitle {
    color: rgba(255, 255, 255, 0.7);
    font-size: 16px;
    font-weight: 400;
    letter-spacing: 0.3px;
}

.form-group {
    margin-bottom: 24px;
}

.form-label {
    display: block;
    margin-bottom: 10px;
    font-weight: 600;
    color: #FFD700;
    font-size: 15px;
    letter-spacing: 0.3px;
}

.form-input {
    width: 100%;
    padding: 18px 20px;
    border: 2px solid rgba(255, 215, 0, 0.2);
    border-radius: 16px;
    font-size: 16px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    background: rgba(255, 255, 255, 0.05);
    color: #ffffff;
    backdrop-filter: blur(10px);
}

.form-input:focus {
    outline: none;
    border-color: #FFD700;
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 0 0 4px rgba(255, 215, 0, 0.1),
                0 8px 25px rgba(255, 215, 0, 0.15);
    transform: translateY(-2px);
}

.form-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 24px;
    grid-column: 1 / -1;
}

.checkbox {
    width: 20px;
    height: 20px;
    accent-color: #FFD700;
    cursor: pointer;
}

.checkbox-label {
    font-size: 15px;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    line-height: 1.4;
}

.submit-btn {
    grid-column: 1 / -1;
    padding: 20px;
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    color: #000000;
    border: none;
    border-radius: 16px;
    font-size: 18px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    margin: 10px 0 30px 0;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    position: relative;
    overflow: hidden;
}

.submit-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.submit-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(255, 215, 0, 0.4),
                0 5px 15px rgba(0, 0, 0, 0.3);
}

.submit-btn:hover::before {
    left: 100%;
}

.submit-btn:active {
    transform: translateY(-1px);
}

.divider {
    grid-column: 1 / -1;
    text-align: center;
    margin: 20px 0;
    position: relative;
    color: rgba(255, 255, 255, 0.5);
    font-size: 15px;
    font-weight: 500;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.3), transparent);
    z-index: 1;
}

.divider span {
    background: rgba(0, 0, 0, 0.85);
    padding: 0 20px;
    position: relative;
    z-index: 2;
}

.social-login {
    grid-column: 1 / -1;
    display: flex;
    gap: 16px;
    margin-bottom: 20px;
}

.social-btn {
    flex: 1;
    padding: 16px 20px;
    border: 2px solid rgba(255, 215, 0, 0.2);
    border-radius: 16px;
    background: rgba(255, 255, 255, 0.05);
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    font-size: 15px;
}

.social-btn:hover {
    border-color: #FFD700;
    background: rgba(255, 215, 0, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.2);
}

.forgot-password {
    grid-column: 1 / -1;
    text-align: center;
    margin-top: 20px;
}

.forgot-password a {
    color: #FFD700;
    text-decoration: none;
    font-size: 15px;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.forgot-password a::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #FFD700, #FFA500);
    transition: width 0.3s ease;
}

.forgot-password a:hover::after {
    width: 100%;
}

@media (max-width: 1024px) {
    .auth-container {
        max-width: 600px;
        padding: 40px 30px;
    }

    .form-container {
        gap: 40px;
    }

    .auth-form.active {
        gap: 20px;
    }
}

@media (max-width: 768px) {
    .auth-container {
        max-width: 500px;
        padding: 30px 25px;
        min-height: auto;
    }

    .form-container {
        display: block;
    }

    .auth-form.active {
        display: block;
        grid-template-columns: none;
    }

    .form-header,
    .checkbox-group,
    .submit-btn,
    .divider,
    .social-login,
    .forgot-password {
        grid-column: auto;
    }

    .social-login {
        flex-direction: column;
    }

    .form-title {
        font-size: 28px;
    }
}

@media (max-width: 480px) {
    .auth-container {
        padding: 25px 20px;
        margin: 10px;
        border-radius: 20px;
    }

    .form-title {
        font-size: 24px;
    }

    .toggle-btn {
        padding: 14px 20px;
        font-size: 15px;
    }
}
